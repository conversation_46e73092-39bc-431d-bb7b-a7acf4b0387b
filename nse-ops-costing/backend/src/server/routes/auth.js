const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const JWTService = require('../utils/jwt');
const emailService = require('../utils/email');
const logger = require('../utils/logger');
const { authMiddleware } = require('../middleware/auth');
const config = require('../config/config');
const fs = require('fs');
const path = require('path');
const jwt = require('jsonwebtoken');
const tokenUtils = require('../utils/jwt');
const Redis = require('ioredis');

const redis = new Redis({
    host: config.REDIS_HOST,
    port: config.REDIS_PORT,
    password: config.REDIS_PASSWORD
});

// Login route
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // Validate input
        if (!username || !password) {
            logger.warn('Login attempt with missing credentials', {
                username: username ? 'provided' : 'missing',
                ip: req.ip
            });
            return res.status(400).json({ message: 'Username and password are required' });
        }

        // Get user from database
        const [users] = await req.db.query('SELECT * FROM users WHERE username = ?', [username]);
        const user = users[0];

        if (!user) {
            logger.warn('Login attempt with invalid username', {
                username,
                ip: req.ip
            });
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Verify password
        // Note: bcrypt.compare() does not use hash rounds directly; it checks the password against the stored hash,
        // and the number of rounds (cost factor) is embedded in the hash itself.
        // The default cost when generating a hash with bcrypt.hash() is 10, but compare() just reads it from the hash.
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            logger.warn('Login attempt with invalid password', {
            username,
            ip: req.ip
            });
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Generate tokens
        const accessToken = jwt.sign(
            { id: user.id, username: user.username, role: user.role },
            config.JWT_SECRET,
            { expiresIn: '50m' }
        );

        const refreshToken = jwt.sign(
            { id: user.id },
            config.JWT_SECRET,
            { expiresIn: '7d' }
        );

        // Store refresh token in Redis
        await redis.set(`refresh_token:${user.id}`, refreshToken, 'EX', 7 * 24 * 60 * 60);

        logger.info('User logged in successfully', {
            userId: user.id,
            username: user.username,
            role: user.role
        });

        res.json({
            token: accessToken,
            refreshToken,
            user: {
                id: user.id,
                username: user.username,
                role: user.role
            }
        });
    } catch (error) {
        logger.error('Login error', {
            error: error.message,
            username: req.body.username,
            ip: req.ip
        });
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Logout route
router.post('/logout', authMiddleware, async (req, res) => {
    try {
        const token = req.headers.authorization.split(' ')[1];
        await JWTService.invalidateToken(token);
        res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
        logger.error('Logout error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Refresh token route
router.post('/refresh-token', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            return res.status(400).json({ error: 'Refresh token is required' });
        }

        const tokens = await JWTService.refreshToken(refreshToken);
        res.json(tokens);
    } catch (error) {
        logger.error('Token refresh error:', error);
        res.status(401).json({ error: 'Invalid refresh token' });
    }
});

// Request password reset
router.post('/request-password-reset', async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            logger.warn('Forgot password attempt without email', {
                ip: req.ip
            });
            return res.status(400).json({ message: 'Email is required' });
        }

        // Get user by email
        const [users] = await req.db.query('SELECT * FROM users WHERE email = ?', [email]);
        const user = users[0];

        if (!user) {
            // Don't reveal if email exists or not
            logger.info('Password reset requested for non-existent email', {
                email,
                ip: req.ip
            });
            return res.json({ message: 'If an account exists with this email, you will receive a password reset link.' });
        }

        // Generate reset token
        const resetToken = jwt.sign(
            { id: user.id },
            config.JWT_SECRET,
            { expiresIn: '1h' }
        );

        // Store token in Redis
        await redis.set(`reset_token:${user.id}`, resetToken, 'EX', 3600);

        // Send email using EmailService
        await emailService.sendPasswordResetEmail(user, resetToken);

        logger.info('Password reset email sent', {
            userId: user.id,
            email,
            ip: req.ip
        });

        res.json({ message: 'If an account exists with this email, you will receive a password reset link.' });
    } catch (error) {
        logger.error('Forgot password error', {
            error: error.message,
            email: req.body.email,
            ip: req.ip
        });
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Reset password
router.post('/reset-password', async (req, res) => {
    try {
        const { token, newPassword } = req.body;

        if (!token || !newPassword) {
            logger.warn('Reset password attempt with missing data', {
                hasToken: !!token,
                hasPassword: !!newPassword,
                ip: req.ip
            });
            return res.status(400).json({ message: 'Token and new password are required' });
        }

        // Verify token
        const decoded = jwt.verify(token, config.JWT_SECRET);
        const storedToken = await redis.get(`reset_token:${decoded.id}`);

        if (!storedToken || storedToken !== token) {
            logger.warn('Reset password attempt with invalid token', {
                userId: decoded.id,
                ip: req.ip
            });
            return res.status(400).json({ message: 'Invalid or expired token' });
        }

        // Hash new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Update password
        await req.db.query('UPDATE users SET password = ? WHERE id = ?', [hashedPassword, decoded.id]);

        // Delete used token
        await redis.del(`reset_token:${decoded.id}`);

        logger.info('Password reset successful', {
            userId: decoded.id,
            ip: req.ip
        });

        res.json({ message: 'Password has been reset successfully' });
    } catch (error) {
        logger.error('Reset password error', {
            error: error.message,
            ip: req.ip
        });
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Health check endpoint for readiness and liveness probes
router.get('/health', (req, res) => {
    res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

module.exports = router;
require('dotenv').config();

module.exports = {
    // Server Configuration
    PORT: process.env.PORT || 3001,
    NODE_ENV: process.env.NODE_ENV || 'production',
    
    // JWT Configuration
    JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',
    JWT_EXPIRATION: process.env.JWT_EXPIRATION || '24h',
    JWT_REFRESH_EXPIRATION: process.env.JWT_REFRESH_EXPIRATION || '7d',
    
    // Redis Configuration
    REDIS_HOST: process.env.REDIS_HOST || 'redis-master.redis.svc.cluster.local',
    REDIS_PORT: process.env.REDIS_PORT || 6379,
    REDIS_PASSWORD: process.env.REDIS_PASSWORD || 'FCfxRKmFDN',
    
    // Email Configuration (for password reset)
    SMTP_HOST: process.env.SMTP_HOST || 'smtp.gmail.com',
    SMTP_PORT: process.env.SMTP_PORT || 587,
    SMTP_USER: process.env.SMTP_USER || '<EMAIL>',
    SMTP_PASS: process.env.SMTP_PASS || 'nbtx fahz vcsv ulfx',
    EMAIL_FROM: process.env.EMAIL_FROM || '<EMAIL>',
    
    // Database Configuration
    DB_HOST: process.env.DB_HOST || 'mysql-service',
    DB_PORT: process.env.DB_PORT || 3306,
    DB_USER: process.env.DB_USER || 'root',
    DB_PASSWORD: process.env.DB_PASSWORD || 'strong_password',
    DB_NAME: process.env.DB_NAME || 'nse_costing',
    
    // Frontend URL (for password reset links)
    FRONTEND_URL: process.env.FRONTEND_URL || 'http://**********:30000',
    
    // ELK Stack Configuration
    ELASTICSEARCH_HOST: process.env.ELASTICSEARCH_HOST || 'quickstart-es-http.elastic-system.svc.cluster.local',
    ELASTICSEARCH_PORT: process.env.ELASTICSEARCH_PORT || 9200,
    ELASTICSEARCH_USERNAME: process.env.ELASTICSEARCH_USERNAME || 'elastic',
    ELASTICSEARCH_PASSWORD: process.env.ELASTICSEARCH_PASSWORD || 'uyQBp8v9xhJ4TM45U9930rv9'
}; 
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('../utils/logger');

const authMiddleware = (req, res, next) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];
        
        if (!token) {
            logger.warn('Authentication attempt without token', {
                path: req.path,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({ message: 'Authentication required' });
        }

        const decoded = jwt.verify(token, config.JWT_SECRET);
        req.user = decoded;

        logger.info('User authenticated', {
            userId: decoded.id,
            username: decoded.username,
            path: req.path,
            method: req.method
        });

        next();
    } catch (error) {
        logger.error('Authentication failed', {
            error: error.message,
            path: req.path,
            method: req.method,
            ip: req.ip
        });
        return res.status(401).json({ message: 'Invalid token' });
    }
};

// Role-based access control middleware
const roleMiddleware = (roles) => {
    return (req, res, next) => {
        try {
            if (!req.user) {
                logger.warn('Role check attempted without authentication', {
                    path: req.path,
                    method: req.method,
                    ip: req.ip
                });
                return res.status(401).json({ message: 'Authentication required' });
            }

            if (!roles.includes(req.user.role)) {
                logger.warn('Unauthorized role access attempt', {
                    userId: req.user.id,
                    username: req.user.username,
                    role: req.user.role,
                    requiredRoles: roles,
                    path: req.path,
                    method: req.method
                });
                return res.status(403).json({ message: 'Insufficient permissions' });
            }

            logger.info('Role check passed', {
                userId: req.user.id,
                username: req.user.username,
                role: req.user.role,
                path: req.path,
                method: req.method
            });

            next();
        } catch (error) {
            logger.error('Role check failed', {
                error: error.message,
                path: req.path,
                method: req.method,
                ip: req.ip
            });
            return res.status(500).json({ message: 'Internal server error' });
        }
    };
};

module.exports = {
    authMiddleware,
    roleMiddleware
}; 
const logger = require('../utils/logger');

const requestLogger = (req, res, next) => {
    const start = Date.now();

    // Log the request
    logger.info(`Incoming ${req.method} request to ${req.path} from ${req.ip}`);

    // Log request body for non-GET requests
    if (req.method !== 'GET' && req.body) {
        logger.debug(`Request body: ${JSON.stringify(req.body)}`);
    }

    // Capture response data
    const originalEnd = res.end;
    const chunks = [];

    res.write = function(chunk) {
        chunks.push(Buffer.from(chunk));
        return originalWrite.apply(this, arguments);
    };

    res.end = function(chunk, encoding) {
        if (chunk) {
            chunks.push(Buffer.from(chunk));
        }

        const duration = Date.now() - start;
        const responseBody = Buffer.concat(chunks).toString('utf8');

        // Log the response
        logger.info(`Response for ${req.method} ${req.path} - Status: ${res.statusCode} - Duration: ${duration}ms`);

        // Log response body for errors
        if (res.statusCode >= 400) {
            logger.error(`Error response: ${responseBody}`);
        }

        originalEnd.call(this, chunk, encoding);
    };

    next();
};

module.exports = requestLogger; 
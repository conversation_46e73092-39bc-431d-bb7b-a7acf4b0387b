#!/bin/bash

# Output file for SQL insert statements
OUTPUT_FILE="insert_statements04.sql"

# Clear the output file if it exists
> "$OUTPUT_FILE"

# Function to escape single quotes for SQL strings
escape_sql_string() {
    echo "$1" | sed "s/'/''/g"
}

# Skip header and read the CSV line by line
# We'll read the entire line first, then use awk to parse the fields,
# as awk is much better at handling quoted CSV fields with embedded commas.
tail -n +2 "OPS-0425.csv" | while IFS= read -r line; do
    # Use awk to parse the CSV line, respecting double quotes
    # -F, sets comma as field separator
    # This awk command is designed to handle standard CSV, where fields can be quoted.
    # It prints each field on a new line, which we then read back into variables.
    parsed_fields=$(echo "$line" | awk -F',' '{
        # Build a string that awk can parse where quoted fields are treated as single fields.
        # This is a common awk pattern for CSV parsing.
        # Initialize an array for fields
        delete a;
        n = 0;
        in_quote = 0;
        current_field = "";

        for (i=1; i<=NF; i++) {
            f = $i;
            if (in_quote) {
                current_field = current_field "," f;
                if (substr(f, length(f), 1) == "\"") {
                    # End of quoted field
                    current_field = substr(current_field, 1, length(current_field)-1); # Remove closing quote
                    a[++n] = current_field;
                    current_field = "";
                    in_quote = 0;
                }
            } else {
                if (substr(f, 1, 1) == "\"" && substr(f, length(f), 1) != "\"") {
                    # Start of quoted field
                    in_quote = 1;
                    current_field = substr(f, 2); # Remove opening quote
                } else if (substr(f, 1, 1) == "\"" && substr(f, length(f), 1) == "\"") {
                    # Quoted field with no internal commas
                    a[++n] = substr(f, 2, length(f)-2); # Remove both quotes
                } else {
                    # Non-quoted field
                    a[++n] = f;
                }
            }
        }
        # Print out the parsed fields, one per line, after trimming
        for (j=1; j<=n; j++) {
            # Trim whitespace from each field
            gsub(/^[[:space:]]+|[[:space:]]+$/, "", a[j]);
            print a[j];
        }
    }')

    # Read the parsed fields back into variables
    # This assumes the order and number of fields are consistent
    readarray -t fields <<< "$parsed_fields"

    # Map parsed fields to variable names
    OPS="${fields[0]}"
    CTCL="${fields[1]}"
    TAP_IP="${fields[2]}"
    BILLING_FROM="${fields[3]}"
    BILLING_UPTO="${fields[4]}"
    CLIENT="${fields[5]}"
    USER="${fields[6]}"
    REMARKS="${fields[7]}"
    RACK="${fields[8]}"
    SEGMENT="${fields[9]}"

    # --- Start of SQL Logic (mostly same as before, but with corrected inputs) ---

    # Condition 1: Set trader_id based on CLIENT or USER
    TRADER_ID="$(escape_sql_string "$USER")"
    CLIENT_ID="$(escape_sql_string "$CLIENT")"

    # Condition 2: session_id is same as CTCL ID
    SESSION_ID="$CTCL"

    # Map session_type
    SESSION_TYPE="HFT"
    # Check for "MIDF" within the USER field (case-insensitive for robustness)
    if [[ "${USER^^}" == *"MIDF"* ]]; then
        SESSION_TYPE="MFT"
    fi

    # Format dates to YYYY-MM-DD
    # The 'sed' command reorders the date parts.
    # 'date' command then formats it to the desired YYYY-MM-DD.
    # Handle potential empty date fields gracefully

    # Format dates to YYYY-MM-DD or NULL
    START_DATE_SQL="NULL" # Default to NULL
    if [[ -n "$BILLING_FROM" ]]; then
        # If not empty, format the date and enclose in single quotes for SQL
        FORMATTED_DATE=$(date -d "$(echo "$BILLING_FROM" | sed 's/\([0-9]\{2\}\)\/\([0-9]\{2\}\)\/\([0-9]\{4\}\)/\3-\2-\1/')" +%Y-%m-%d)
        START_DATE_SQL="'$FORMATTED_DATE'"
    fi

    END_DATE_SQL="NULL" # Default to NULL
    if [[ -n "$BILLING_UPTO" ]]; then
        # If not empty, format the date and enclose in single quotes for SQL
        FORMATTED_DATE=$(date -d "$(echo "$BILLING_UPTO" | sed 's/\([0-9]\{2\}\)\/\([0-9]\{2\}\)\/\([0-9]\{4\}\)/\3-\2-\1/')" +%Y-%m-%d)
        END_DATE_SQL="'$FORMATTED_DATE'"
    fi

    # Construct the SQL INSERT statement
    # Use START_DATE_SQL and END_DATE_SQL directly, they will either be 'YYYY-MM-DD' or NULL
    SQL_STATEMENT="INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('$TRADER_ID', '$CLIENT_ID', '$SESSION_ID', '$SESSION_TYPE', $OPS, $START_DATE_SQL, $END_DATE_SQL, '$(escape_sql_string "$SEGMENT")');"

    # Append to the output file
    echo "$SQL_STATEMENT" >> "$OUTPUT_FILE"

done

echo "SQL insert statements generated in $OUTPUT_FILE"

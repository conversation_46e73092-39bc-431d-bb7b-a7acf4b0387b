INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('Dropcopy', '', '520200000', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('Dropcopy', '', '520200001', 'HFT', 10, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('BOLT', '', '520200902', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('BOLT', '', '520201001', 'HFT', 10, '2025-05-01', '2025-05-31', 'CDS');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200201', 'HFT', 10, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200202', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('Saral', '', '520200203', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('Saral', '', '520200204', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01, BKA001, SYLI01_THETA, RAH01, STTL01, ARV001, NITA001, KIRIT001, SPB001, SVS01_THETA, HSG001, SAM001,METRO01,AMPL01,KVEE01', '520200205', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01,BKA001,SYLI01,RAH01,STTL01,ARV001,NITA001,KIRTI001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01', '520200206', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'COSHU01, PPB01, SBE01, MAHA01, AAR01, BHA001, ALPHA01, TIV001, JAI001, SAL01_THETA, BETA(QICM_CP),BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01', '520200207', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001,ALPHA01,TIV001,JAI001,SAL01,BHARAT01,INFI01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01', '520200208', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01,MNI01,BKA001,SYLI01,RAH01,STTL01,ARV001,NITA001,KIRTI001,SPB001,SVS01,HSG001,SAM001FPPL01,METRO01,AMPL01,KVEE01,CAHLLP01', '520200209', 'MFT', 1500, '2025-05-01', '2025-05-18', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01,MNI01,BKA001,SYLI01,RAH01,STTL01,ARV001,NITA001,KIRTI001,SPB001,SVS01,HSG001,SAM001,FPPL01,METRO01,AMPL01,KVEE01,CAHLLP01,KGR001', '520200209', 'MFT', 1500, '2025-05-19', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001,ALPHA01,TIV001,JAI001,SAL01,BHARAT01,INFI01,ORIC01,SUBH001,SSMC01,MTSL01,SSPV001,ARIA01, FFPL01', '520200210', 'MFT', 1500, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'PPB01', '520200211', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_PRO', 'PRO', '520200212', 'MFT', 100, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200213', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_PRO', 'PRO', '520200214', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('hf6_ak', 'Pro', '520200215', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'Pro', '520200216', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'Pro', '520200217', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'Pro', '520200218', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'Pro', '520200219', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'Pro', '520200220', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('hf6_ak', 'Pro', '520200221', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200222', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200223', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200224', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200225', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200226', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200227', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200228', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200229', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200230', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200231', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200232', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200233', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200234', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200235', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200236', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200237', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200238', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF11', 'Pro', '520200239', 'HFT', 1000, '2025-05-01', '2025-05-31', 'CM');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF', 'MIPL01', '520200240', 'MFT', 500, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF', 'KPPL01', '520200241', 'MFT', 500, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200242', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF7_UP', 'Pro', '520200243', 'HFT', 50, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200244', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200245', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200246', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('BRAHMASTRA', '', '520200247', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'PRO', '520200248', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'PRO', '520200249', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'PRO', '520200250', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF21', 'METRO01, ORIC01, SBE01, SHUB001, AMPL01, SUBH001, KVEE01, STTL01, ARV001, MTSL01, BHARAT01, SVS01, SAM001, SYLI01,  SSS01, EIPL01, FPPL01', '520200251', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF6_BV', 'PRO', '520200252', 'HFT', 50, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200253', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200254', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF', 'ART01', '520200255', 'MFT', 500, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200256', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF10', 'PRO', '520200257', 'HFT', 50, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'PRO', '520200258', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'Pro', '520200259', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01, MNI01, BKA001, SYLI01_THETA, RAH01, STTL01, ARV001, NITA001, KIRIT001, SPB001, SVS01_THETA, HSG001, SAM001, FPPL01,METRO01,AMPL01,KVEE01', '520200260', 'MFT', 2000, '2025-05-01', '2025-05-07', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01, MNI01, BKA001, SYLI01_THETA, RAH01, STTL01, ARV001, NITA001, KIRIT001, SPB001, SVS01_THETA, HSG001, SAM001, FPPL01,METRO01,AMPL01,KVEE01,DYDX001', '520200260', 'MFT', 2000, '2025-05-08', '2025-05-18', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'SSS01, MNI01, BKA001, SYLI01_THETA, RAH01, STTL01, ARV001, NITA001, KIRIT001, SPB001, SVS01_THETA, HSG001, SAM001, FPPL01,METRO01,AMPL01,KVEE01,DYDX001,HARSH01', '520200260', 'MFT', 2000, '2025-05-19', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'COSHU01, PPB01, SBE01, MAHA01, AAR01, BHA001, ALPHA01, TIV001, JAI001, SAL01_THETA, BETA(QICM_CP),BHARAT01, FPPL01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,JYOTI01,ATC01', '520200261', 'MFT', 2000, '2025-05-01', '2025-05-06', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'COSHU01, PPB01, SBE01, MAHA01, AAR01, BHA001, ALPHA01, TIV001, JAI001, SAL01_THETA, BETA(QICM_CP),BHARAT01, FPPL01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,JYOTI01,ATC01,KPL01', '520200261', 'MFT', 2000, '2025-05-07', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'INFI01', '520200262', 'MFT', 1000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'INFI01', '520200263', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'INFI01, SSS01', '520200264', 'MFT', 10, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200265', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200266', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200267', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200268', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200269', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200270', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200271', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200272', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200273', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'CRIMSON', '520200354', 'MFT', 500, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MODERN', '520200355', 'MFT', 500, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'HUSH01', '520200356', 'MFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200357', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF5D', 'PRO', '520200358', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF5D', 'PRO', '520200359', 'HFT', 250, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200360', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200361', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200362', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200363', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200364', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', 'Pro', '520200365', 'HFT', 0, '2025-05-01', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', 'Pro', '520200365', 'HFT', 1000, '2025-05-27', '2025-05-27', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', 'Pro', '520200365', 'HFT', 0, '2025-05-28', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', 'Pro', '520200366', 'HFT', 0, '2025-05-01', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', 'Pro', '520200366', 'HFT', 1000, '2025-05-27', '2025-05-27', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', 'Pro', '520200366', 'HFT', 0, '2025-05-28', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200367', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF10', 'Pro', '520200368', 'HFT', 50, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200369', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF5', 'Pro', '520200370', 'HFT', 100, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200371', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200372', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200373', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200374', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200375', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200376', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200377', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200378', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200379', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200380', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200381', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200382', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200383', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200384', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200385', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200386', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200387', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200388', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200389', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200390', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200391', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200392', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200393', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200394', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200395', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200396', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200397', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200398', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200399', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200400', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200401', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200402', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200403', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200404', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200405', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF10', '', '520200406', 'HFT', 50, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', '', '520200407', 'HFT', 1000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF8_PRO', '', '520200408', 'HFT', 1000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_PRO', 'PRO', '520200409', 'MFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200410', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MIPL01', '520200411', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'ART01', '520200412', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200413', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'KPPL01', '520200414', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200415', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200416', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'CRIMSON', '520200417', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MODERN', '520200418', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200419', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200420', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200421', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200422', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200423', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200424', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200425', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200426', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200427', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200428', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200429', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200430', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MIPL01', '520200431', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MIPL01', '520200432', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'ART01', '520200433', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'ART01', '520200434', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'KPPL01', '520200435', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'KPPL01', '520200436', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200437', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200438', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200439', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', '', '520200440', 'HFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'CRIMSON', '520200441', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'CRIMSON', '520200442', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MODERN', '520200443', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('MIDF_THETA', 'MODERN', '520200444', 'MFT', 0, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200445', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200446', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200447', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200448', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200449', 'HFT', 2000, '2025-05-01', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200450', 'HFT', 0, '2025-05-01', '2025-05-04', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200450', 'HFT', 2000, '2025-05-05', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200451', 'HFT', 0, '2025-05-01', '2025-05-04', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200451', 'HFT', 2000, '2025-05-05', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200452', 'HFT', 0, '2025-05-01', '2025-05-04', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200452', 'HFT', 2000, '2025-05-05', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200453', 'HFT', 0, '2025-05-01', '2025-05-04', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200453', 'HFT', 2000, '2025-05-05', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('General', 'HUSH01', '520200454', 'HFT', 0, '2025-05-01', '2025-05-04', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200454', 'HFT', 2000, '2025-05-05', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200455', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200455', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200455', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200456', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200456', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200456', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200457', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200457', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200457', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200458', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200458', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200458', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200459', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200459', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200459', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200460', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200460', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200460', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200461', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200461', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200461', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200462', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200462', 'HFT', 0, '2025-05-23', '2025-05-26', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200462', 'HFT', 2000, '2025-05-27', '2025-05-31', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200463', 'HFT', 0, '2025-05-23', '2025-05-27', 'FNO');
INSERT INTO TraderSessions (trader_id, client_id, session_id, session_type, ops_per_session, start_date, end_date, segment) VALUES ('HF29', 'HUSH01', '520200463', 'HFT', 2000, '2025-05-28', '2025-05-31', 'FNO');

import React from 'react';
import { BrowserRouter as Router, Routes, Route, NavLink } from 'react-router-dom';
import Input from './components/Input';
import Bill from './components/Bill';
import ViewActive from './components/ViewActive';
import './App.css'; // Ensure this CSS file is created

const App = () => {
  return (
    <Router>
      <div className="app-container">
        {/* Header */}
        <header className="app-header">
          <h1>BSE FNO OPS Costing</h1>
          <nav>
            <NavLink to="/input" className="nav-link" activeClassName="active">
              Input
            </NavLink>
            <NavLink to="/bill" className="nav-link" activeClassName="active">
              Bills
            </NavLink>
            <NavLink to="/viewactive" className="nav-link" activeClassName="active">
              View Active
            </NavLink>
          </nav>
        </header>

        {/* Main Content */}
        <div className="content">
          <Routes>
            <Route path="/" element={<Input />} /> {/* Set Input as default page */}
            <Route path="/input" element={<Input />} />
            <Route path="/bill" element={<Bill />} />
            <Route path="/viewactive" element={<ViewActive />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
};

export default App;
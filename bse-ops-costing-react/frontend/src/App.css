/* General styles */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
}

/* Header styles */
.app-header {
  background-color: #000000;
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 24px;
}

/* Navigation styles */
nav {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link.active,
.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Content styles */
.content {
  flex: 1;
  padding: 20px;
  background-color: white;
  margin: 20px auto;
  max-width: 1200px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  nav {
    flex-direction: column;
    gap: 10px;
  }

  .app-header h1 {
    font-size: 20px;
  }
}

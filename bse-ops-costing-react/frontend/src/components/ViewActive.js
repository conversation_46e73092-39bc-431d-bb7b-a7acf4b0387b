import React, { useState, useEffect } from 'react';
import axios from 'axios';
import config from '../config';

// Utility to safely extract YYYY-MM-DD from any date string
function toYYYYMMDD(dateString) {
  if (!dateString) return '';
  // Handles both 'YYYY-MM-DD' and 'YYYY-MM-DDTHH:mm:ss.sssZ'
  return dateString.length >= 10 ? dateString.slice(0, 10) : dateString;
}

const ViewActive = () => {
  const [activeSessions, setActiveSessions] = useState([]);
  const [endDates, setEndDates] = useState({}); // Store end dates for each session

  useEffect(() => {
    fetchActiveSessions();
  }, []);

  const fetchActiveSessions = async () => {
    try {
      const response = await axios.get(`${config.API_BASE_URL}/api/active`);
      setActiveSessions(response.data);
    } catch (error) {
      console.error('Error fetching active sessions:', error);
    }
  };

  const handleUpdate = async (sessionId, traderId, clientId, firmName, sessionType, segment, startDate, opsPerSession, linkType) => {
    if (!endDates[traderId + "," + sessionId]) {
      alert('Please select an end date.');
      return;
    }
    try {
      await axios.post(`${config.API_BASE_URL}/api/update`, {
        session_id: sessionId,
        trader_id: traderId,
        client_id: clientId,
        firm_name: firmName,
        session_type: sessionType,
        segment: segment,
        start_date: toYYYYMMDD(startDate),
        ops_per_session: opsPerSession,
        end_date: toYYYYMMDD(endDates[traderId + "," + sessionId]),
        link_type: linkType
      });
      
      alert('Session updated successfully!');
      window.location.reload();
      fetchActiveSessions();
    } catch (error) {
      console.error('Error updating session:', error);
    }
  };

  const handleEndDateChange = (traderId,sessionId, value) => {
    setEndDates((prevState) => ({
      ...prevState,
      [traderId + "," + sessionId]: value
    }));
  };

  return (
    <div>
      <h2>View Active Page</h2>
      {activeSessions.length === 0 ? (
        <p>No active sessions</p>
      ) : (
        <table style={{ borderCollapse: 'collapse', width: '100%', border: '1px solid black' }}>
          <thead>
            <tr>
              <th style={{ border: '1px solid black', padding: '8px' }}>Trader ID</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Client ID</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Session ID</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Session Type</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Segment</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Link Type</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Start Date</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Ops per Session</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Input End Date</th>
              <th style={{ border: '1px solid black', padding: '8px' }}>Firm Name</th>
            </tr>
          </thead>
          <tbody>
            {activeSessions.map((session) => (
              <tr key={session.session_id}>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.trader_id}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.client_id}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.session_id}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.session_type}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.segment}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.link_type}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{toYYYYMMDD(session.start_date)}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.ops_per_session}</td>
                <td style={{ border: '1px solid black', padding: '8px' }}>
                  <input
                    type="date"
                    placeholder="End Date"
                    value={endDates[session.trader_id + "," + session.session_id] || ''}
                    onChange={(e) => handleEndDateChange(session.trader_id, session.session_id, e.target.value)}
                  />
                  <button onClick={() => handleUpdate(
                    session.session_id,
                    session.trader_id,
                    session.client_id,
                    session.firm_name,
                    session.session_type,
                    session.segment,
                    session.start_date,
                    session.ops_per_session,
                    session.link_type
                  )}>
                    Update
                  </button>
                </td>
                <td style={{ border: '1px solid black', padding: '8px' }}>{session.firm_name}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default ViewActive;
import React, { useState } from 'react';
import axios from 'axios';
import config from '../config';

const Bill = () => {
  const [month, setMonth] = useState('');
  const [year, setYear] = useState('');
  const [bills, setBills] = useState([]);
  const [total, setTotal] = useState(0);

  // Map month numbers to month names
  const months = {
    1: 'January',
    2: 'February',
    3: 'March',
    4: 'April',
    5: 'May',
    6: 'June',
    7: 'July',
    8: 'August',
    9: 'September',
    10: 'October',
    11: 'November',
    12: 'December'
  };

  // Available years
  const years = [2024, 2025];

  const handleFetchBills = async () => {
    if (!month || !year) {
      alert('Please select both month and year.');
      return;
    }
    try {
      const response = await axios.post(`${config.API_BASE_URL}/api/bill`, { month, year, OnlyBill: true });
      setBills(response.data.finalBill);
      setTotal(response.data.totalBill);
    } catch (error) {
      console.error('Error fetching bills:', error);
    }
  };

  const handleRecalculate = async () => {
    if (!month || !year) {
      alert('Please select both month and year.');
      return;
    }
    try {
      await axios.post(`${config.API_BASE_URL}/api/bill`, { month, year, ReCalculate: true });
      alert('Bills recalculated successfully!');
      handleFetchBills(); // Fetch updated bills after recalculation
    } catch (error) {
      console.error('Error recalculating bills:', error);
    }
  };

  return (
    <div>
      <h2>Bills Page</h2>
      <div>
        <label>Month:</label>
        <select value={month} onChange={(e) => setMonth(e.target.value)} required>
          <option value="">Select Month</option>
          {Object.entries(months).map(([key, value]) => (
            <option key={key} value={key}>
              {value}
            </option>
          ))}
        </select>
        <label>Year:</label>
        <select value={year} onChange={(e) => setYear(e.target.value)} required>
          <option value="">Select Year</option>
          {years.map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
        <button onClick={handleFetchBills}>Fetch Bills</button>
        <button onClick={handleRecalculate}>Recalculate</button>
      </div>
      {bills.length > 0 ? (
        <>
          <p>Total Bill: {total}</p>
          <table style={{ borderCollapse: 'collapse', width: '100%', border: '1px solid black' }}>
            <thead>
              <tr>
                <th style={{ border: '1px solid black', padding: '8px' }}>Trader ID</th>
                <th style={{ border: '1px solid black', padding: '8px' }}>Monthly Bill Amount</th>
              </tr>
            </thead>
            <tbody>
              {bills.map((bill, index) => (
                <tr key={index}>
                  <td style={{ border: '1px solid black', padding: '8px' }}>{bill.trader_id}</td>
                  <td style={{ border: '1px solid black', padding: '8px' }}>{bill.monthly_bill_amount}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </>
      ) : (
        <p>No bills found for the selected month and year.</p>
      )}
    </div>
  );
};

export default Bill;
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import config from '../config';
import CreatableSelect from 'react-select/creatable';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const Input = () => {
  const [formData, setFormData] = useState({
    traderID: [],
    clientID: [],
    sessionID: [],
    firmName: '', // Add firmName
    sessionType: 'HFT',
    segment: 'FNO',
    opsPerSession: '',
    startDate: null,
    endDate: null,
    linkType: 'Direct'
  });
  const [traderOptions, setTraderOptions] = useState([]);
  const [clientOptions, setClientOptions] = useState([]); // Added clientOptions
  const [sessionOptions, setSessionOptions] = useState([]);
  const [firmOptions, setFirmOptions] = useState([]); // Add firmOptions
  const [submittedForms, setSubmittedForms] = useState([]);

  useEffect(() => {
    axios.get(`${config.API_BASE_URL}/api/trader-ids`)
      .then(response => {
        const options = response.data.map(id => ({ value: id, label: id }));
        setTraderOptions(options);
      })
      .catch(error => console.error('Error fetching trader IDs:', error));
  }, []);

  useEffect(() => {
    axios.get(`${config.API_BASE_URL}/api/client-ids`)
      .then(response => {
        const options = response.data.map(id => ({ value: id, label: id }));
        setClientOptions(options);
      })
      .catch(error => console.error('Error fetching client IDs:', error));
  }, []);

  useEffect(() => {
    axios.get(`${config.API_BASE_URL}/api/session-ids`)
      .then(response => {
        const options = response.data.map(id => ({ value: id, label: id }));
        setSessionOptions(options);
      })
      .catch(error => console.error('Error fetching session IDs:', error));
  }, []);

  useEffect(() => {
    axios.get(`${config.API_BASE_URL}/api/firm-names`)
      .then(response => {
        const options = response.data.map(id => ({ value: id, label: id }));
        setFirmOptions(options);
      })
      .catch(error => console.error('Error fetching firm names:', error));
  }, []);

  useEffect(() => {
    axios.get(`${config.API_BASE_URL}/api/submitted-forms`)
      .then(response => {
        setSubmittedForms(response.data);
      })
      .catch(error => console.error('Error fetching submitted forms:', error));
  }, []);

  const handleTraderChange = (selectedOptions) => {
    setFormData({ ...formData, traderID: selectedOptions || [] });
  };

  const handleClientChange = (selectedOptions) => {
    setFormData({ ...formData, clientID: selectedOptions || [] });
  };

  const handleSessionChange = (selectedOption) => {
    setFormData({ ...formData, sessionID: selectedOption.value });
  };

  const handleFirmNameChange = (selectedOption) => {
    setFormData({ ...formData, firmName: selectedOption ? selectedOption.value : '' });
  };

  const handleSessionTypeChange = (e) => {
    setFormData({ ...formData, sessionType: e.target.value });
  };

  const handleSegmentChange = (e) => {
    setFormData({ ...formData, segment: e.target.value });
  };

  const handleOpsPerSessionChange = (e) => {
    setFormData({ ...formData, opsPerSession: e.target.value });
  };

  const handleStartDateChange = (date) => {
    const formattedDate = date.toISOString().split('T')[0];
    setFormData({ ...formData, startDate: formattedDate });
  };

  const handleEndDateChange = (date) => {
    const formattedDate = date.toISOString().split('T')[0];
    setFormData({ ...formData, endDate: formattedDate });
  };

  const handleLinkTypeChange = (e) => {
    setFormData({ ...formData, linkType: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const traderIDs = formData.traderID.map(t => t.value).join('|');
      const clientIDs = formData.clientID.map(c => c.value).join('|');
      const payload = {
        ...formData,
        traderID: traderIDs,
        clientID: clientIDs,
        firmName: formData.firmName,
        linkType: formData.linkType
      };

      const response = await axios.post(`${config.API_BASE_URL}/api/input`, { data: payload });
      alert(response.data.message);
    } catch (error) {
      console.error('Error submitting data:', error);
    }
  };

  return (
    <div style={{ maxWidth: '6000px', margin: '20px auto', padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
      <h2>Input Page</h2>
      <form onSubmit={handleSubmit} style={{ display: 'grid', gap: '20px' }}>
        <div>
          <label htmlFor="traderID">Trader ID:</label>
          <CreatableSelect
            inputId="traderID"
            isMulti
            options={traderOptions}
            value={formData.traderID}
            onChange={handleTraderChange}
            placeholder="Select or type Trader IDs"
            isClearable
            formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
            required
          />
        </div>

        <div>
          <label htmlFor="clientID">Client ID:</label>
          <CreatableSelect
            inputId="clientID"
            isMulti
            options={clientOptions}
            value={formData.clientID}
            onChange={handleClientChange}
            placeholder="Select or type Client IDs"
            isClearable
            formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
            required
          />
        </div>

        <div>
          <label htmlFor="sessionID">Session ID:</label>
          <CreatableSelect
            inputId="sessionID"
            isMulti={false}
            options={sessionOptions}
            value={sessionOptions.find(option => option.value === formData.sessionID)}
            onChange={handleSessionChange}
            placeholder="Select or type Session ID"
            isClearable
            formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
            required
          />
        </div>

        <div>
          <label htmlFor="firmName">Firm Name:</label>
          <CreatableSelect
            inputId="firmName"
            isMulti={false}
            options={firmOptions}
            value={firmOptions.find(option => option.value === formData.firmName) || null}
            onChange={handleFirmNameChange}
            placeholder="Select or type Firm Name"
            isClearable
            formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
            required
          />
        </div>

        <div>
          <label htmlFor="sessionType">Session Type:</label>
          <select
            id="sessionType"
            name="sessionType"
            value={formData.sessionType}
            onChange={handleSessionTypeChange}
            required
            style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            <option value="HFT">HFT</option>
            <option value="MFT">MFT</option>
          </select>
        </div>

        <div>
          <label htmlFor="segment">Segment:</label>
          <select
            id="segment"
            name="segment"
            value={formData.segment}
            onChange={handleSegmentChange}
            required
            style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            <option value="FNO">FNO</option>
            <option value="CM">CM</option>
            <option value="CDS">CDS</option>
          </select>
        </div>

        <div>
          <label htmlFor="opsPerSession">OPS per Session:</label>
          <select
            id="opsPerSession"
            name="opsPerSession"
            value={formData.opsPerSession}
            onChange={handleOpsPerSessionChange}
            required
            style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            <option value="">Select OPS per Session</option>
            {[0,10, 50, 100, 250, 500, 750, 1000, 1250, 1500, 1750, 2000].map(value => (
              <option key={value} value={value}>{value}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="startDate">Start Date:</label>
          <DatePicker
            id="startDate"
            selected={formData.startDate}
            onChange={handleStartDateChange}
            dateFormat="dd/MM/yyyy"
            placeholderText="Select Start Date"
            required
            className="date-picker"
          />
        </div>

        <div>
          <label htmlFor="endDate">End Date:</label>
          <DatePicker
            id="endDate"
            selected={formData.endDate}
            onChange={handleEndDateChange}
            dateFormat="dd/MM/yyyy"
            placeholderText="Select End Date"
            className="date-picker"
          />
        </div>

        <div>
          <label htmlFor="linkTypeDirect">Link Type:</label>
          <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
            <label htmlFor="linkTypeDirect">
              <input
                type="radio"
                id="linkTypeDirect"
                name="linkType"
                value="Direct"
                checked={formData.linkType === 'Direct'}
                onChange={handleLinkTypeChange}
                required
              /> Direct
            </label>
            <label htmlFor="linkTypeSwitch">
              <input
                type="radio"
                id="linkTypeSwitch"
                name="linkType"
                value="Switch"
                checked={formData.linkType === 'Switch'}
                onChange={handleLinkTypeChange}
              /> Switch
            </label>
          </div>
        </div>

        <button type="submit" style={{ padding: '10px', background: '#007bff', color: '#fff', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
          Submit
        </button>
      </form>
      <div style={{ marginTop: '40px' }}>
        <h3>Submitted Forms:</h3>
        {submittedForms.length > 0 ? (
          <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '20px' }}>
            <thead>
              <tr style={{ backgroundColor: '#f4f4f4', borderBottom: '1px solid #ccc' }}>
                <th style={{ padding: '10px', textAlign: 'left' }}>Trader ID</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Client ID</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Session ID</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Firm Name</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Session Type</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Segment</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>OPS per Session</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Start Date</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>End Date</th>
                <th style={{ padding: '10px', textAlign: 'left' }}>Link Type</th>
              </tr>
            </thead>
            <tbody>
              {submittedForms.map((form, index) => (
                <tr key={form.sessionID + '-' + (form.traderID || '') + '-' + (form.clientID || '')} style={{ borderBottom: '1px solid #ccc', ':hover': { backgroundColor: '#f9f9f9' } }}>
                  <td style={{ padding: '10px' }}>{form.traderID}</td>
                  <td style={{ padding: '10px' }}>{form.clientID}</td>
                  <td style={{ padding: '10px' }}>{form.sessionID}</td>
                  <td style={{ padding: '10px' }}>{form.firmName}</td>
                  <td style={{ padding: '10px' }}>{form.sessionType}</td>
                  <td style={{ padding: '10px' }}>{form.segment}</td>
                  <td style={{ padding: '10px' }}>{form.opsPerSession}</td>
                  <td style={{ padding: '10px' }}>{form.startDate}</td>
                  <td style={{ padding: '10px' }}>{form.endDate}</td>
                  <td style={{ padding: '10px' }}>{form.linkType}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>No forms submitted yet.</p>
        )}
      </div>
    </div>
  );
};

export default Input;
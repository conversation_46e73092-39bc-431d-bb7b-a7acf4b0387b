apiVersion: apps/v1
kind: Deployment
metadata:
  name: bse-ops-costing-frontend-deployment
  labels:
    app: bse-ops-costing-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bse-ops-costing-frontend
  template:
    metadata:
      labels:
        app: bse-ops-costing-frontend
    spec:
      containers:
        - name: frontend
          image: abhinav173/cicdimages:bse-ops-costing-frontend-latest
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
      imagePullSecrets:
        - name: my-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: bse-ops-costing-frontend-service
spec:
  type: NodePort
  selector:
    app: bse-ops-costing-frontend
  ports:
    - name: frontend-port
      port: 80
      targetPort: 80
      protocol: TCP
      nodePort: 31000

apiVersion: apps/v1
kind: Deployment
metadata:
  name: bse-ops-costing-backend-deployment
  labels:
    app: bse-ops-costing-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bse-ops-costing-backend
  template:
    metadata:
      labels:
        app: bse-ops-costing-backend
    spec:
      containers:
        - name: backend
          image: abhinav173/cicdimages:bse-ops-costing-backend-latest
          ports:
            - containerPort: 3310
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
      imagePullSecrets:
        - name: my-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: bse-ops-costing-backend-service
spec:
  type: NodePort
  selector:
    app: bse-ops-costing-backend
  ports:
    - name: backend-port
      port: 3310
      targetPort: 3310
      protocol: TCP
      nodePort: 31001

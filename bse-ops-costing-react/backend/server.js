const express = require('express');
const mysql = require('mysql2');
const bodyParser = require('body-parser');
const cors = require('cors');

const app = express();
const port = 3310;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Local array to store submitted forms
let submittedForms = [];

// MySQL connection pool
const pool = mysql.createPool({
  host: "**********",//'mysql-service',
  port: 30006,
  user: 'root',
  password: 'strong_password',
  database: 'bse_costing', // Replace with your database name
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  timezone: '+05:30' // Set timezone to IST (Asia/Kolkata)
}).promise();

// Endpoint to handle form submissions
app.post('/api/input', async (req, res) => {
  const { data } = req.body;

  if (!data) {
    return res.status(400).json({ message: 'Invalid request payload' });
  }

  // Extract form data
  const { traderID, clientID, sessionID, firmName, sessionType, segment, opsPerSession, startDate, endDate, linkType } = data;

  // Debug logging
  //console.log('Received /api/input POST:');
  //console.log('Raw data:', data);
  //console.log('Parsed startDate:', startDate, 'endDate:', endDate);

  // SQL query to insert data into the database
  const query = `
    INSERT INTO TraderSessions (trader_id, client_id, session_id, firm_name, session_type, ops_per_session, start_date, end_date, segment, link_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  try {
    const results = await pool.execute(query, [traderID, clientID, sessionID, firmName, sessionType, opsPerSession, startDate, endDate, segment, linkType]);
    // Log what was inserted
    //console.log('Inserted into DB:', {
    //  traderID,
    //  clientID,
    // sessionID,
    //  firmName,
    //  sessionType,
    //  segment,
    //  opsPerSession,
    //  startDate,
    //  endDate,
    //  linkType
    //});
    // Add the submitted form to the local array
    submittedForms.push({
      traderID,
      clientID,
      sessionID,
      firmName,
      sessionType,
      segment,
      opsPerSession,
      startDate,
      endDate,
      linkType
    });
    return res.json({ message: 'Form submitted successfully' });

  } catch (err) {
    console.error('Error executing SQL query:', err);
    return res.status(500).json({ message: 'Database error' });
  }
});


// Endpoint to fetch submitted forms
app.get('/api/submitted-forms', (req, res) => {
  res.json(submittedForms);
});


// Fetch active sessions endpoint
app.get('/api/active', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM TraderSessions WHERE end_date IS NULL');
    // Log what is being sent to frontend
    //console.log('Fetched active sessions:', rows.map(r => ({ session_id: r.session_id, start_date: r.start_date })));
    // Convert start_date and end_date to YYYY-MM-DD in IST before sending
    const toISTDateString = (date) => {
      if (!date) return '';
      const d = new Date(date);
      // Convert to IST by adding 5.5 hours (19800000 ms)
      d.setTime(d.getTime() + (5.5 * 60 * 60 * 1000));
      return d.toISOString().slice(0, 10);
    };
    const rowsIST = rows.map(row => ({
      ...row,
      start_date: toISTDateString(row.start_date),
      end_date: toISTDateString(row.end_date)
    }));
    res.json(rowsIST);
  } catch (err) {
    console.error('Error fetching active sessions:', err);
    res.status(500).send('Error fetching active sessions');
  }
});

// Update session endpoint
app.post('/api/update', async (req, res) => {
  const { session_id, trader_id, client_id, firm_name, session_type, segment, start_date, ops_per_session, end_date, link_type } = req.body;

  try {
    await pool.query(
      'UPDATE TraderSessions SET end_date = ?, client_id = ?, firm_name = ?, link_type = ? WHERE session_id = ? AND trader_id = ? AND session_type = ? AND segment = ? AND start_date = ? AND ops_per_session = ?',
      [end_date, client_id, firm_name, link_type, session_id, trader_id, session_type, segment, start_date, ops_per_session]
    );
    res.redirect('/viewactive');
  } catch (err) {
    console.error('Error updating session:', err);
    res.status(500).send('Error updating session');
  }
});

// Fetch trader IDs endpoint
app.get('/api/trader-ids', async (req, res) => {
  try {
    // Call the stored procedure `get_distinct_trader_ids`
    const [rows] = await pool.query('CALL get_distinct_trader_ids()');
    
    // Extract trader IDs from the result
    const traderIDs = rows[0].map(row => row.trader_id);
    
    // Return the trader IDs as the response
    res.json(traderIDs);
  } catch (err) {
    console.error('Error fetching trader IDs:', err);
    res.status(500).send('Error fetching trader IDs');
  }
});

// Fetch session IDs endpoint
app.get('/api/client-ids', async (req, res) => {
  try {
    // Call the stored procedure `get_distinct_trader_ids`
    const [rows] = await pool.query('CALL get_distinct_client_ids()');
    
    // Extract trader IDs from the result
    const clientIDs = rows[0].map(row => row.client_id);
    
    // Return the trader IDs as the response
    res.json(clientIDs);
  } catch (err) {
    console.error('Error fetching session IDs:', err);
    res.status(500).send('Error fetching session IDs');
  }
});

// Fetch session IDs endpoint
app.get('/api/session-ids', async (req, res) => {
  try {
    // Call the stored procedure `get_distinct_trader_ids`
    const [rows] = await pool.query('CALL get_distinct_session_ids()');
    
    // Extract trader IDs from the result
    const sessionIDs = rows[0].map(row => row.session_id);
    
    // Return the trader IDs as the response
    res.json(sessionIDs);
  } catch (err) {
    console.error('Error fetching session IDs:', err);
    res.status(500).send('Error fetching session IDs');
  }
});

// Bill calculation endpoint
app.post('/api/bill', async (req, res) => {
  const { month, year, OnlyBill, ReCalculate } = req.body;

  try {
    if (OnlyBill) {

      if (!month || !year) {
        return res.status(400).json({ message: 'Month and year are required' });
      }
    
      try {
        // Call the stored procedure `fetch_monthly_bills`
        const [results] = await pool.query('CALL bse_costing.fetch_monthly_bills(?, ?)', [month, year]);
    
        // Extract the rows from the result
        const rows = results[0];
    
        // Calculate the total bill
        const totalBill = rows.reduce((sum, row) => sum + row.monthly_bill_amount, 0);
    
        // Return the final bill details and total bill
        res.json({ finalBill: rows, totalBill });
      } catch (err) {
        console.error('Error fetching monthly bills:', err);
        res.status(500).json({ message: 'Error fetching monthly bills' });
      }

    } else if (ReCalculate) {

      if (!month || !year) {
        return res.status(400).json({ message: 'Month and year are required' });
      }
    
      try {
        // Call the stored procedure `monthly_bills_wrapper`
        await pool.query('CALL bse_costing.monthly_bills_wrapper(?, ?)', [month, year]);
    
        // Send success response
        res.json({ message: 'Bills recalculated successfully!' });
      } catch (err) {
        console.error('Error recalculating bills:', err);
        res.status(500).json({ message: 'Error recalculating bills' });
      }

    } else {
      res.status(400).send('Invalid request');
    }
  } catch (err) {
    console.error('Error processing bill request:', err);
    res.status(500).send('Error processing bill request');
  }
});

// Fetch firm names endpoint
app.get('/api/firm-names', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT DISTINCT firm_name FROM TraderSessions WHERE firm_name IS NOT NULL AND firm_name != ""');
    const firmNames = rows.map(row => row.firm_name);
    res.json(firmNames);
  } catch (err) {
    console.error('Error fetching firm names:', err);
    res.status(500).send('Error fetching firm names');
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Server running at http://127.0.0.1:${port}`);
});